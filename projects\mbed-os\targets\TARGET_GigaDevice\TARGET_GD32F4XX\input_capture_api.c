#include "input_capture_api.h"

#if DEVICE_INPUT_CAPTURE

#include "cmsis.h"
#include "pinmap.h"
#include "PeripheralPins.h"

#define TIMER_NUM TIMER4
#define TIMER_CLOCK RCU_TIMER4
#define TIMER_IRQ_TYPE TIMER4_IRQn
#define TIMER_CALLBACK  TIMER4_IRQHandler

static uint32_t ic1value = 0;
static uint32_t ic2value = 0;
static uint16_t dutycycle = 0;
static uint16_t frequency = 0;

void input_capture_init_time(void)
{
  timer_ic_parameter_struct timer_icinitpara;
  timer_parameter_struct timer_initpara;

  rcu_periph_clock_enable(TIMER_CLOCK);
  rcu_timer_clock_prescaler_config(RCU_TIMER_PSC_MUL4);

  timer_deinit(TIMER_NUM);

  /* TIMER configuration 1MHz*/
  timer_initpara.prescaler         = SystemCoreClock / 1000000 -1;
  timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
  timer_initpara.counterdirection  = TIMER_COUNTER_UP;
  timer_initpara.period            = 65535;
  timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
  timer_initpara.repetitioncounter = 0;
  timer_init(TIMER_NUM,&timer_initpara);

  /* configuration */
  /* CH0 PWM input capture configuration */
  timer_icinitpara.icpolarity  = TIMER_IC_POLARITY_RISING;
  timer_icinitpara.icselection = TIMER_IC_SELECTION_DIRECTTI;
  timer_icinitpara.icprescaler = TIMER_IC_PSC_DIV1;
  timer_icinitpara.icfilter    = 0x0;
  timer_input_pwm_capture_config(TIMER_NUM,TIMER_CH_0,&timer_icinitpara);

  /* slave mode selection: TIMER_NUM */
  timer_input_trigger_source_select(TIMER_NUM,TIMER_SMCFG_TRGSEL_CI0FE0);
  timer_slave_mode_select(TIMER_NUM,TIMER_SLAVE_MODE_RESTART);

  /* select the master slave mode */
  timer_master_slave_mode_config(TIMER_NUM,TIMER_MASTER_SLAVE_MODE_ENABLE);

  /* auto-reload preload enable */
  timer_auto_reload_shadow_enable(TIMER_NUM);
  /* clear channel 0 interrupt bit */
  timer_interrupt_flag_clear(TIMER_NUM,TIMER_INT_CH0);
  /* channel 0 interrupt enable */
  timer_interrupt_enable(TIMER_NUM,TIMER_INT_CH0);

  /* enable */
  timer_enable(TIMER_NUM);

  //nvic_priority_group_set(NVIC_PRIGROUP_PRE1_SUB3);
  nvic_irq_enable(TIMER_IRQ_TYPE, 1, 1);
}

void input_capture_init_gpio(PinName pin)
{
  rcu_periph_enum port_clock;
  uint32_t gpio;
  uint32_t port = GD_PORT_GET(pin);
  uint32_t gd_pin = 1 << GD_PIN_GET(pin);

  switch(port){
      case 0: gpio = GPIOA;
              port_clock = RCU_GPIOA;
    break;
      case 1: gpio = GPIOB;
              port_clock = RCU_GPIOB;
    break;
      case 2: gpio = GPIOC;
              port_clock = RCU_GPIOC;
    break;
      case 3: gpio = GPIOD;
              port_clock = RCU_GPIOD;
    break;
      case 4: gpio = GPIOE;
              port_clock = RCU_GPIOE;
    break;
      case 5: gpio = GPIOF;
              port_clock = RCU_GPIOF;
    break;
      case 6: gpio = GPIOG;
              port_clock = RCU_GPIOG;
    break;
      case 7: gpio = GPIOH;
              port_clock = RCU_GPIOH;
    break;
      case 8: gpio = GPIOI;
              port_clock = RCU_GPIOI;
    break;
      default:
    break;
  }

  rcu_periph_clock_enable(port_clock);

  gpio_mode_set(gpio, GPIO_MODE_AF, GPIO_PUPD_NONE, gd_pin);
  gpio_output_options_set(gpio, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,gd_pin);
  gpio_af_set(gpio, GPIO_AF_2, gd_pin);
}

void input_capture_init(PinName pin)
{
  input_capture_init_gpio(pin);
  input_capture_init_time();
}

void TIMER_CALLBACK(void)
{
  if(SET == timer_interrupt_flag_get(TIMER4,TIMER_INT_CH0))
  {
    /* clear channel 0 interrupt bit */
    timer_interrupt_flag_clear(TIMER4,TIMER_INT_CH0);
    /* read channel 0 capture value */
    ic1value = timer_channel_capture_value_register_read(TIMER4,TIMER_CH_0)+1;

    if(0 != ic1value)
    {
      /* read channel 1 capture value */
        ic2value = timer_channel_capture_value_register_read(TIMER4,TIMER_CH_1)+1;

      /* calculate the duty cycle value */
      dutycycle = (ic2value * 100) / ic1value;
      /* calculate the frequency value */
      frequency = 1000000 / ic1value;
    }
    else{
      dutycycle = 0;
      frequency = 0;
    }
  }
}

uint16_t input_capture_read_pluse_width_us()
{
  return ic2value;
}

uint16_t input_capture_read_pluse_frequency()
{
  return frequency;
}

#endif
