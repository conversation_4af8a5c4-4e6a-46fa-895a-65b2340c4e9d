/* mbed Microcontroller Library
 * Copyright (c) 2024 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "watchdog_api.h"
#include "gd32h7xx.h"

#if DEVICE_WATCHDOG

/* FWDGT clock frequency is 32KHz */
#define FWDGT_FREQ_HZ           32000U
#define FWDGT_MAX_TIMEOUT_MS    32000U  /* Maximum timeout with DIV256 prescaler */

static uint32_t watchdog_timeout_ms = 0;

watchdog_status_t hal_watchdog_init(const watchdog_config_t *config) 
{
    uint16_t reload_value;
    uint16_t prescaler_div;
    uint32_t timeout_ms = config->timeout_ms;
    
    /* Limit timeout to maximum supported value */
    if (timeout_ms > FWDGT_MAX_TIMEOUT_MS) {
        timeout_ms = FWDGT_MAX_TIMEOUT_MS;
    }
    
    /* Store timeout for later use */
    watchdog_timeout_ms = timeout_ms;
    
    /* Enable write access to FWDGT registers */
    fwdgt_write_enable();
    
    /* Calculate prescaler and reload value based on timeout */
    if (timeout_ms <= 512) {
        /* Use DIV4 prescaler: timeout = reload_value * 4 / 32000 * 1000 */
        prescaler_div = FWDGT_PSC_DIV4;
        reload_value = (timeout_ms * FWDGT_FREQ_HZ) / (4 * 1000);
    } else if (timeout_ms <= 1024) {
        /* Use DIV8 prescaler: timeout = reload_value * 8 / 32000 * 1000 */
        prescaler_div = FWDGT_PSC_DIV8;
        reload_value = (timeout_ms * FWDGT_FREQ_HZ) / (8 * 1000);
    } else if (timeout_ms <= 2048) {
        /* Use DIV16 prescaler: timeout = reload_value * 16 / 32000 * 1000 */
        prescaler_div = FWDGT_PSC_DIV16;
        reload_value = (timeout_ms * FWDGT_FREQ_HZ) / (16 * 1000);
    } else if (timeout_ms <= 4096) {
        /* Use DIV32 prescaler: timeout = reload_value * 32 / 32000 * 1000 */
        prescaler_div = FWDGT_PSC_DIV32;
        reload_value = (timeout_ms * FWDGT_FREQ_HZ) / (32 * 1000);
    } else if (timeout_ms <= 8192) {
        /* Use DIV64 prescaler: timeout = reload_value * 64 / 32000 * 1000 */
        prescaler_div = FWDGT_PSC_DIV64;
        reload_value = (timeout_ms * FWDGT_FREQ_HZ) / (64 * 1000);
    } else if (timeout_ms <= 16384) {
        /* Use DIV128 prescaler: timeout = reload_value * 128 / 32000 * 1000 */
        prescaler_div = FWDGT_PSC_DIV128;
        reload_value = (timeout_ms * FWDGT_FREQ_HZ) / (128 * 1000);
    } else {
        /* Use DIV256 prescaler: timeout = reload_value * 256 / 32000 * 1000 */
        prescaler_div = FWDGT_PSC_DIV256;
        reload_value = (timeout_ms * FWDGT_FREQ_HZ) / (256 * 1000);
    }
    
    /* Ensure reload value is within valid range (0-4095) */
    if (reload_value > 4095) {
        reload_value = 4095;
    }
    if (reload_value == 0) {
        reload_value = 1;
    }
    
    /* Configure FWDGT */
    if (ERROR == fwdgt_config(reload_value, prescaler_div)) {
        return WATCHDOG_STATUS_INVALID_ARGUMENT;
    }
    
    /* Enable FWDGT */
    fwdgt_enable();
    
    return WATCHDOG_STATUS_OK;
}

void hal_watchdog_kick(void) 
{
    fwdgt_counter_reload();
}

watchdog_status_t hal_watchdog_stop(void)
{
    /* Note: FWDGT cannot be stopped once started, only reset can stop it */
    return WATCHDOG_STATUS_NOT_SUPPORTED;
}

uint32_t hal_watchdog_get_reload_value(void)
{
    return watchdog_timeout_ms;
}

watchdog_features_t hal_watchdog_get_platform_features(void)
{
    watchdog_features_t features;
    
    features.max_timeout = FWDGT_MAX_TIMEOUT_MS;
    features.update_config = 0;  /* Cannot update config once started */
    features.disable_watchdog = 0;  /* Cannot disable once started */
    features.clock_typical_frequency = FWDGT_FREQ_HZ;
    features.clock_max_frequency = FWDGT_FREQ_HZ;
    
    return features;
}

#endif /* DEVICE_WATCHDOG */
