/* mbed Microcontroller Library
 * Copyright (c) 2024 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "analogout_api.h"
#include "mbed_assert.h"

#if DEVICE_ANALOGOUT

#include "cmsis.h"
#include "pinmap.h"
#include "mbed_error.h"
#include "PeripheralPins.h"

#define DEV_DAC_ACCURACY_12BIT (0xFFF) // 12 bits
#define DEV_DAC_BITS  (12)

/** 初始化模拟输出外设
 *
 * 配置模拟输出使用的引脚
 * @param obj 要初始化的模拟输出对象
 * @param pin 模拟输出引脚名称
 */
void analogout_init(dac_t *obj, PinName pin)
{
    /* 从引脚获取外设名称并分配给对象 */
    obj->dac = (DACName)pinmap_peripheral(pin, PinMap_DAC);
    MBED_ASSERT(obj->dac != (DACName)NC);

    /* 获取引脚功能并分配使用的通道给对象 */
    uint32_t function = pinmap_function(pin, PinMap_DAC);
    MBED_ASSERT(function != (uint32_t)NC);

    obj->channel = GD_PIN_CHANNEL_GET(function);
    MBED_ASSERT(obj->channel <= DAC_OUT1);

    /* 配置GPIO */
    pinmap_pinout(pin, PinMap_DAC);

    /* 保存引脚以备将来使用 */
    obj->pin = pin;

    /* 使能DAC时钟 */
    rcu_periph_clock_enable(RCU_DAC);

    /* 配置DAC */
    dac_wave_mode_config(obj->dac, obj->channel, DAC_WAVE_DISABLE);
    dac_trigger_disable(obj->dac, obj->channel);
    dac_mode_config(obj->dac, obj->channel, NORMAL_PIN_BUFFON);

    /* 使能DAC */
    dac_enable(obj->dac, obj->channel);
    analogout_write_u16(obj, 0);
}

/** 释放模拟输出对象
 *
 * 注意：这在mbed驱动程序中目前未使用
 * @param obj 模拟输出对象
 */
void analogout_free(dac_t *obj)
{
    /* 复位DAC并禁用时钟 */
    dac_deinit(obj->dac);
    rcu_periph_clock_disable(RCU_DAC);

    /* 配置GPIO */
    /* 获取引脚功能并分配使用的通道给对象 */
    uint32_t function = pinmap_function(obj->pin, PinMap_DAC);
    MBED_ASSERT(function != (uint32_t)NC);

    pin_function(obj->pin, function);
}

/** 设置指定整数值的输出电压
 *
 * @param obj 模拟输入对象
 * @param value 要设置的整数输出电压值
 */
static inline void dev_dac_data_set(dac_t *obj, uint16_t value)
{
    dac_data_set(obj->dac, obj->channel, DAC_ALIGN_12B_R, (value & DEV_DAC_ACCURACY_12BIT));

    dac_enable(obj->dac, obj->channel);
}

/** 获取当前DAC数据
 *
 * @param obj 模拟输入对象
 * @return DAC数据
 */
static inline uint16_t dev_dac_data_get(dac_t *obj)
{
    return (uint16_t)dac_output_value_get(obj->dac, obj->channel);
}

/** 设置输出电压，以百分比（浮点数）指定
 *
 * @param obj 模拟输入对象
 * @param value 要设置的浮点输出电压值
 */
void analogout_write(dac_t *obj, float value)
{
    if (value < 0.0f) {
        /* 当值小于0.0时，设置DAC输出数据为0 */
        dev_dac_data_set(obj, 0);
    } else if (value > 1.0f) {
        /* 当值大于1.0时，设置DAC输出数据为0xFFF */
        dev_dac_data_set(obj, (uint16_t)DEV_DAC_ACCURACY_12BIT);
    } else {
        /* 当值在0.0到1.0范围内时，计算DAC输出数据 */
        dev_dac_data_set(obj, (uint16_t)(value * (float)DEV_DAC_ACCURACY_12BIT));
    }
}

/** 设置输出电压，以无符号16位指定
 *
 * @param obj 模拟输入对象
 * @param value 要设置的无符号16位输出电压值
 */
void analogout_write_u16(dac_t *obj, uint16_t value)
{
    dev_dac_data_set(obj, value >> (16 - DEV_DAC_BITS));
}

/** 读取引脚上的当前电压值
 *
 * @param obj 模拟输入对象
 * @return 表示引脚上当前电压的浮点值，
 *     以百分比测量
 */
float analogout_read(dac_t *obj)
{
    uint16_t ret_val = dev_dac_data_get(obj);
    return (float)ret_val * (1.0f / (float)DEV_DAC_ACCURACY_12BIT);
}

/** Read the current voltage value on the pin, as a normalized unsigned 16-bit value
 *
 * @param obj The analogin object
 * @return An unsigned 16-bit value representing the current voltage on the pin
 */
uint16_t analogout_read_u16(dac_t *obj)
{
    uint16_t ret_val = dev_dac_data_get(obj);
    return (uint16_t)((ret_val << 4) | ((ret_val >> 8) & 0x000F));
}

const PinMap *analogout_pinmap()
{
    return PinMap_DAC;
}

#endif /* DEVICE_ANALOGOUT */
