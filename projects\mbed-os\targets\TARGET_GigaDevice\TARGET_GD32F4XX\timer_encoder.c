#include "timer_encoder.h"

#if DEVICE_ENCODER_PLUSE

#include "cmsis.h"
#include "pinmap.h"
#include "PeripheralPins.h"

#define TIMER_NUM TIMER1
#define TIMER_CLOCK RCU_TIMER1
#define TIMER_CHANNEL_A TIMER_CH_0
#define TIMER_CHANNEL_B TIMER_CH_1
#define TIMER_IRQ_TYPE TIMER1_IRQn
#define TIMER_CALLBACK  TIMER1_IRQHandler

static uint32_t encoder_pluse_counts = 0;
static uint32_t timer_start_counter_counts = 1000000000;

void timer_encoder_init(void)
{
  timer_ic_parameter_struct timer_icinitpara;
  timer_parameter_struct timer_initpara;

  rcu_periph_clock_enable(TIMER_CLOCK);
  rcu_timer_clock_prescaler_config(RCU_TIMER_PSC_MUL4);

  timer_deinit(TIMER_NUM);

  /* TIMER configuration 1MHz*/
  timer_initpara.prescaler         = 0;
  timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
  timer_initpara.counterdirection  = TIMER_COUNTER_UP;
  timer_initpara.period            = 0xffffffff;
  timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
  timer_initpara.repetitioncounter = 0;
  timer_init(TIMER_NUM, &timer_initpara);

  timer_channel_input_struct_para_init(&timer_icinitpara);

  /* configuration */
  /* CH0 PWM input capture configuration */
  timer_icinitpara.icpolarity  = TIMER_IC_POLARITY_RISING;
  timer_icinitpara.icselection = TIMER_IC_SELECTION_DIRECTTI;
  timer_icinitpara.icprescaler = TIMER_IC_PSC_DIV1;
  timer_icinitpara.icfilter    = 0x0;

  timer_input_capture_config(TIMER_NUM, TIMER_CHANNEL_A, &timer_icinitpara);
  timer_input_capture_config(TIMER_NUM, TIMER_CHANNEL_B, &timer_icinitpara); 

  timer_quadrature_decoder_mode_config(TIMER_NUM, TIMER_ENCODER_MODE2, TIMER_IC_POLARITY_RISING, TIMER_IC_POLARITY_RISING);

  timer_counter_value_config(TIMER_NUM, timer_start_counter_counts);
  /* enable */
  timer_enable(TIMER_NUM);



  // nvic_irq_enable(TIMER_IRQ_TYPE, 1, 1);
}

void gpio_encoder_init(PinName pin_a, PinName pin_b)
{
  rcu_periph_enum port_clock_a;
  rcu_periph_enum port_clock_b;  
  uint32_t gpio_a, gpio_b;
  uint32_t port_a = GD_PORT_GET(pin_a);
  uint32_t gd_pin_a = 1 << GD_PIN_GET(pin_a);
  uint32_t port_b = GD_PORT_GET(pin_b);
  uint32_t gd_pin_b = 1 << GD_PIN_GET(pin_b);

  switch(port_a){
      case 0: gpio_a = GPIOA;
              port_clock_a = RCU_GPIOA;
    break;
      case 1: gpio_a = GPIOB;
              port_clock_a = RCU_GPIOB;
    break;
      case 2: gpio_a = GPIOC;
              port_clock_a = RCU_GPIOC;
    break;
      case 3: gpio_a = GPIOD;
              port_clock_a = RCU_GPIOD;
    break;
      case 4: gpio_a = GPIOE;
              port_clock_a = RCU_GPIOE;
    break;
      case 5: gpio_a = GPIOF;
              port_clock_a = RCU_GPIOF;
    break;
      case 6: gpio_a = GPIOG;
              port_clock_a = RCU_GPIOG;
    break;
      case 7: gpio_a = GPIOH;
              port_clock_a = RCU_GPIOH;
    break;
      case 8: gpio_a = GPIOI;
              port_clock_a = RCU_GPIOI;
    break;
      default:
    break;
  }

  switch(port_b){
      case 0: gpio_b = GPIOA;
              port_clock_b = RCU_GPIOA;
    break;
      case 1: gpio_b = GPIOB;
              port_clock_b = RCU_GPIOB;
    break;
      case 2: gpio_b = GPIOC;
              port_clock_b = RCU_GPIOC;
    break;
      case 3: gpio_b = GPIOD;
              port_clock_b = RCU_GPIOD;
    break;
      case 4: gpio_b = GPIOE;
              port_clock_b = RCU_GPIOE;
    break;
      case 5: gpio_b = GPIOF;
              port_clock_b = RCU_GPIOF;
    break;
      case 6: gpio_b = GPIOG;
              port_clock_b = RCU_GPIOG;
    break;
      case 7: gpio_b = GPIOH;
              port_clock_b = RCU_GPIOH;
    break;
      case 8: gpio_b = GPIOI;
              port_clock_b = RCU_GPIOI;
    break;
      default:
    break;
  }  

  rcu_periph_clock_enable(port_clock_a);
  rcu_periph_clock_enable(port_clock_b);  

  gpio_mode_set(gpio_a, GPIO_MODE_AF, GPIO_PUPD_NONE, gd_pin_a);
  gpio_output_options_set(gpio_a, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,gd_pin_a);
  gpio_af_set(gpio_a, GPIO_AF_1, gd_pin_a);

  gpio_mode_set(gpio_b, GPIO_MODE_AF, GPIO_PUPD_NONE, gd_pin_b);
  gpio_output_options_set(gpio_b, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,gd_pin_b);
  gpio_af_set(gpio_b, GPIO_AF_1, gd_pin_b);  
}

void encoder_init(PinName pin_a, PinName pin_b)
{
  gpio_encoder_init(pin_a, pin_b);
  timer_encoder_init();
}

void TIMER_CALLBACK(void)
{

}

int32_t get_encoder_pluse_counts() {
  encoder_pluse_counts = timer_counter_read(TIMER_NUM) - timer_start_counter_counts;
  return encoder_pluse_counts;
}

#endif  // DEVICE_ENCODER_PLUSE
