/* mbed Microcontroller Library
 * Copyright (c) 2024 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef MBED_OBJECTS_H
#define MBED_OBJECTS_H

#include "cmsis.h"
#include "PortNames.h"
#include "PeripheralNames.h"
#include "PinNames.h"
#include "gpio_object.h"

#ifdef __cplusplus
extern "C" {
#endif

struct gpio_s {
    PinName pin;
    uint32_t mask;
    uint32_t gpio_periph;
};

typedef struct {
    uint32_t adc;
    uint32_t channel;
} analogin_s;

typedef struct {
    uint32_t uart;
    uint32_t index;
    uint32_t baudrate;
    uint32_t databits;
    uint32_t stopbits;
    uint32_t parity;
#if DEVICE_SERIAL_FC
    uint32_t hw_flow_ctl;
#endif
    PinName pin_tx;
    PinName pin_rx;
    operation_state_enum tx_state;
    operation_state_enum rx_state;
} serial_s;

typedef struct {
    uint32_t spi;
    uint32_t index;
} spi_s;

typedef struct {
    uint32_t pwm;
    uint32_t channel;
} pwmout_s;

typedef struct {
    I2CName i2c;
    uint32_t index;
    PinName sda;
    PinName scl;
    uint32_t freq;
    operation_state_enum state;
    operation_state_enum previous_state_mode;
    uint32_t global_trans_option;
    bool use_soft;
#if DEVICE_I2CSLAVE
    uint8_t slave;
#endif
} i2c_s;

#if DEVICE_ANALOGOUT
typedef struct {
    DACName dac;
    uint32_t channel;
    PinName pin;
} dac_s;
#endif

#if DEVICE_FLASH
typedef struct {
    uint32_t dummy;
} flash_s;
#endif

#if DEVICE_TRNG
typedef struct {
    uint32_t dummy;
} trng_s;
#endif

typedef struct {
    PortName port;
    uint32_t mask;
    PinDirection direction;
    __IO uint32_t *reg_in;
    __IO uint32_t *reg_out;
} port_s;

#if DEVICE_CAN
typedef struct {
    CANName can;
    uint32_t index;
} can_s;
#endif

#ifdef __cplusplus
}
#endif

#endif 