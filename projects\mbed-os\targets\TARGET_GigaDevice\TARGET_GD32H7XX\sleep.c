/* mbed Microcontroller Library
 * Copyright (c) 2024 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#if DEVICE_SLEEP

#include "sleep_api.h"
#include "us_ticker_api.h"
#include "mbed_critical.h"
#include "mbed_error.h"

extern void ticker_timer_data_save(void);
extern void ticker_timer_data_restore(void);
extern int serial_busy_state_check(void);
extern int mbed_sdk_inited;

/*!
    \brief      configure the system clock to 600M by PLL which selects HXTAL(25M) as its clock source
    \param[in]  none
    \param[out] none
    \retval     none
*/
static void system_clock_600m_25m_hxtal(void)
{
    uint32_t timeout = 0U;
    uint32_t stab_flag = 0U;

    /* enable HXTAL */
    RCU_CTL |= RCU_CTL_HXTALEN;

    /* wait until HXTAL is stable or the startup time is longer than HXTAL_STARTUP_TIMEOUT */
    do {
        timeout++;
        stab_flag = (RCU_CTL & RCU_CTL_HXTALSTB);
    } while ((0U == stab_flag) && (HXTAL_STARTUP_TIMEOUT != timeout));

    /* if fail */
    if (0U == (RCU_CTL & RCU_CTL_HXTALSTB)) {
        while (1) {
        }
    }

    /* enable PMU */
    rcu_periph_clock_enable(RCU_PMU);
    
    /* set voltage scaling to scale 0 for maximum performance */
    pmu_voltage_scaling_config(PMU_SCALE0_MODE);

    /* HXTAL is stable */
    /* AHB = SYSCLK */
    RCU_CFG0 |= RCU_AHB_CKSYS_DIV1;
    /* APB3 = AHB/2 */
    RCU_CFG0 |= RCU_APB3_CKAHB_DIV2;
    /* APB1 = AHB/4 */
    RCU_CFG0 |= RCU_APB1_CKAHB_DIV4;
    /* APB2 = AHB/2 */
    RCU_CFG0 |= RCU_APB2_CKAHB_DIV2;
    /* APB4 = AHB/4 */
    RCU_CFG0 |= RCU_APB4_CKAHB_DIV4;

    /* Configure the main PLL, PSC = 5, PLL_N = 120, PLL_P = 1, PLL_Q = 5, PLL_R = 2 */
    /* PLL = 25MHz / 5 * 120 / 1 = 600MHz */
    RCU_PLL0 = (5U | (120U << 4U) | (0U << 16U) | (RCU_PLL0SRC_HXTAL) | (5U << 20U) | (2U << 24U));

    /* enable PLL */
    RCU_CTL |= RCU_CTL_PLL0EN;

    /* wait until PLL is stable */
    while (0U == (RCU_CTL & RCU_CTL_PLL0STB)) {
    }

    /* select PLL as system clock */
    RCU_CFG0 &= ~RCU_CFG0_SCS;
    RCU_CFG0 |= RCU_CKSYSSRC_PLL0P;

    /* wait until PLL is selected as system clock */
    while (0U == (RCU_CFG0 & RCU_SCSS_PLL0P)) {
    }
}


/** Send the microcontroller to sleep
 *
 * The processor is setup ready for sleep, and sent to sleep. In this mode, the
 * system clock to the core is stopped until a reset or an interrupt occurs. This eliminates
 * dynamic power used by the processor, memory systems and buses. The processor, peripheral and
 * memory state are maintained, and the peripherals continue to work and can generate interrupts.
 *
 * The processor can be woken up by any internal peripheral interrupt or external pin interrupt.
 *
 * The wake-up time shall be less than 10 us.
 *
 */
void hal_sleep(void)
{
    /* disable interrupts */
    core_util_critical_section_enter();

    /* enter SLEEP mode */
    pmu_to_sleepmode(WFI_CMD);

    /* enable interrupts */
    core_util_critical_section_exit();
}


/** Send the microcontroller to deep sleep
 *
 * This processor is setup ready for deep sleep, and sent to sleep using __WFI(). This mode
 * has the same sleep features as sleep plus it powers down peripherals and high frequency clocks.
 * All state is still maintained.
 *
 * The processor can only be woken up by low power ticker, RTC, an external interrupt on a pin or a watchdog timer.
 *
 * The wake-up time shall be less than 10 ms.
 */
void hal_deepsleep(void)
{
    if (0 != serial_busy_state_check()) {
        return;
    }

    /* disable interrupts */
    core_util_critical_section_enter();

    ticker_timer_data_save();

    /* enter DEEP-SLEEP mode */
    rcu_periph_clock_enable(RCU_PMU);
    pmu_to_deepsleepmode(WFI_CMD);

    mbed_sdk_inited = 0;

    /* reconfigure the PLL and set the system clock to the highest frequency after wake up */
    system_clock_600m_25m_hxtal();

    ticker_timer_data_restore();
    mbed_sdk_inited = 1;

    /* enable interrupts */
    core_util_critical_section_exit();
}

#endif /* DEVICE_SLEEP */
