/* mbed Microcontroller Library
 * Copyright (c) 2018 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "PeripheralPins.h"

/* Type Definitions */
typedef uint32_t PinName;
typedef uint32_t PeripheralName;

/* GPIO Mode */
#define GPIO_MODE_INPUT             0x00
#define GPIO_MODE_OUTPUT            0x01
#define GPIO_MODE_AF                0x02
#define GPIO_MODE_ANALOG            0x03

/* <PERSON>IO Pull-up/Pull-down */
#define GPIO_PUPD_NONE             0x00
#define GPIO_PUPD_PULLUP           0x01
#define GPIO_PUPD_PULLDOWN         0x02

/* GPIO Output Type */
#define GPIO_OTYPE_PP              0x00
#define GPIO_OTYPE_OD              0x01

/* GPIO Output Speed */
#define GPIO_OSPEED_2MHZ           0x00
#define GPIO_OSPEED_25MHZ          0x01
#define GPIO_OSPEED_50MHZ          0x02
#define GPIO_OSPEED_200MHZ         0x03

/* GPIO Alternate Function */
#define GPIO_AF_0                  0x00
#define GPIO_AF_1                  0x01
#define GPIO_AF_2                  0x02
#define GPIO_AF_3                  0x03
#define GPIO_AF_4                  0x04
#define GPIO_AF_5                  0x05
#define GPIO_AF_6                  0x06
#define GPIO_AF_7                  0x07
#define GPIO_AF_8                  0x08
#define GPIO_AF_9                  0x09
#define GPIO_AF_10                 0x0A
#define GPIO_AF_11                 0x0B
#define GPIO_AF_12                 0x0C
#define GPIO_AF_13                 0x0D
#define GPIO_AF_14                 0x0E
#define GPIO_AF_15                 0x0F

/* Pin Names */
#define PORTA_0                    ((PinName)0x00)
#define PORTA_1                    ((PinName)0x01)
#define PORTA_2                    ((PinName)0x02)
#define PORTA_3                    ((PinName)0x03)
#define PORTA_4                    ((PinName)0x04)
#define PORTA_5                    ((PinName)0x05)
#define PORTA_6                    ((PinName)0x06)
#define PORTA_7                    ((PinName)0x07)
#define PORTA_8                    ((PinName)0x08)
#define PORTA_9                    ((PinName)0x09)
#define PORTA_10                   ((PinName)0x0A)
#define PORTA_11                   ((PinName)0x0B)
#define PORTA_12                   ((PinName)0x0C)
#define PORTA_13                   ((PinName)0x0D)
#define PORTA_14                   ((PinName)0x0E)
#define PORTA_15                   ((PinName)0x0F)

#define PORTB_0                    ((PinName)0x10)
#define PORTB_1                    ((PinName)0x11)
#define PORTB_2                    ((PinName)0x12)
#define PORTB_3                    ((PinName)0x13)
#define PORTB_4                    ((PinName)0x14)
#define PORTB_5                    ((PinName)0x15)
#define PORTB_6                    ((PinName)0x16)
#define PORTB_7                    ((PinName)0x17)
#define PORTB_8                    ((PinName)0x18)
#define PORTB_9                    ((PinName)0x19)
#define PORTB_10                   ((PinName)0x1A)
#define PORTB_11                   ((PinName)0x1B)
#define PORTB_12                   ((PinName)0x1C)
#define PORTB_13                   ((PinName)0x1D)
#define PORTB_14                   ((PinName)0x1E)
#define PORTB_15                   ((PinName)0x1F)

#define PORTC_0                    ((PinName)0x20)
#define PORTC_1                    ((PinName)0x21)
#define PORTC_2                    ((PinName)0x22)
#define PORTC_3                    ((PinName)0x23)
#define PORTC_4                    ((PinName)0x24)
#define PORTC_5                    ((PinName)0x25)
#define PORTC_6                    ((PinName)0x26)
#define PORTC_7                    ((PinName)0x27)
#define PORTC_8                    ((PinName)0x28)
#define PORTC_9                    ((PinName)0x29)
#define PORTC_10                   ((PinName)0x2A)
#define PORTC_11                   ((PinName)0x2B)
#define PORTC_12                   ((PinName)0x2C)
#define PORTC_13                   ((PinName)0x2D)
#define PORTC_14                   ((PinName)0x2E)
#define PORTC_15                   ((PinName)0x2F)

#define PORTD_0                    ((PinName)0x30)
#define PORTD_1                    ((PinName)0x31)
#define PORTD_2                    ((PinName)0x32)
#define PORTD_3                    ((PinName)0x33)
#define PORTD_4                    ((PinName)0x34)
#define PORTD_5                    ((PinName)0x35)
#define PORTD_6                    ((PinName)0x36)
#define PORTD_7                    ((PinName)0x37)
#define PORTD_8                    ((PinName)0x38)
#define PORTD_9                    ((PinName)0x39)
#define PORTD_10                   ((PinName)0x3A)
#define PORTD_11                   ((PinName)0x3B)
#define PORTD_12                   ((PinName)0x3C)
#define PORTD_13                   ((PinName)0x3D)
#define PORTD_14                   ((PinName)0x3E)
#define PORTD_15                   ((PinName)0x3F)

#define PORTE_0                    ((PinName)0x40)
#define PORTE_1                    ((PinName)0x41)
#define PORTE_2                    ((PinName)0x42)
#define PORTE_3                    ((PinName)0x43)
#define PORTE_4                    ((PinName)0x44)
#define PORTE_5                    ((PinName)0x45)
#define PORTE_6                    ((PinName)0x46)
#define PORTE_7                    ((PinName)0x47)
#define PORTE_8                    ((PinName)0x48)
#define PORTE_9                    ((PinName)0x49)
#define PORTE_10                   ((PinName)0x4A)
#define PORTE_11                   ((PinName)0x4B)
#define PORTE_12                   ((PinName)0x4C)
#define PORTE_13                   ((PinName)0x4D)
#define PORTE_14                   ((PinName)0x4E)
#define PORTE_15                   ((PinName)0x4F)

#define PORTF_0                    ((PinName)0x50)
#define PORTF_1                    ((PinName)0x51)
#define PORTF_2                    ((PinName)0x52)
#define PORTF_3                    ((PinName)0x53)
#define PORTF_4                    ((PinName)0x54)
#define PORTF_5                    ((PinName)0x55)
#define PORTF_6                    ((PinName)0x56)
#define PORTF_7                    ((PinName)0x57)
#define PORTF_8                    ((PinName)0x58)
#define PORTF_9                    ((PinName)0x59)
#define PORTF_10                   ((PinName)0x5A)
#define PORTF_11                   ((PinName)0x5B)
#define PORTF_12                   ((PinName)0x5C)
#define PORTF_13                   ((PinName)0x5D)
#define PORTF_14                   ((PinName)0x5E)
#define PORTF_15                   ((PinName)0x5F)

#define NC                         ((PinName)0xFFFFFFFF)

/* Peripheral Names */
#define ADC_0                      ((PeripheralName)0x00)
#define ADC_1                      ((PeripheralName)0x01)
#define ADC_2                      ((PeripheralName)0x02)
#define ADC_3                      ((PeripheralName)0x03)

#define DAC_0                      ((PeripheralName)0x10)
#define DAC_1                      ((PeripheralName)0x11)

#define I2C_0                      ((PeripheralName)0x20)
#define I2C_1                      ((PeripheralName)0x21)
#define I2C_2                      ((PeripheralName)0x22)
#define I2C_3                      ((PeripheralName)0x23)

#define PWM_0                      ((PeripheralName)0x30)
#define PWM_1                      ((PeripheralName)0x31)
#define PWM_2                      ((PeripheralName)0x32)
#define PWM_3                      ((PeripheralName)0x33)
#define PWM_4                      ((PeripheralName)0x34)
#define PWM_5                      ((PeripheralName)0x35)
#define PWM_6                      ((PeripheralName)0x36)
#define PWM_7                      ((PeripheralName)0x37)
#define PWM_8                      ((PeripheralName)0x38)
#define PWM_9                      ((PeripheralName)0x39)
#define PWM_10                     ((PeripheralName)0x3A)
#define PWM_11                     ((PeripheralName)0x3B)
#define PWM_12                     ((PeripheralName)0x3C)
#define PWM_13                     ((PeripheralName)0x3D)

#define UART_0                     ((PeripheralName)0x40)
#define UART_1                     ((PeripheralName)0x41)
#define UART_2                     ((PeripheralName)0x42)
#define UART_3                     ((PeripheralName)0x43)
#define UART_4                     ((PeripheralName)0x44)
#define UART_5                     ((PeripheralName)0x45)
#define UART_6                     ((PeripheralName)0x46)
#define UART_7                     ((PeripheralName)0x47)
#define UART_8                     ((PeripheralName)0x48)

#define SPI_0                      ((PeripheralName)0x50)
#define SPI_1                      ((PeripheralName)0x51)
#define SPI_2                      ((PeripheralName)0x52)
#define SPI_3                      ((PeripheralName)0x53)
#define SPI_4                      ((PeripheralName)0x54)

#define CAN_0                      ((PeripheralName)0x60)
#define CAN_1                      ((PeripheralName)0x61)
#define CAN_2                      ((PeripheralName)0x62)
#define CAN_3                      ((PeripheralName)0x63)

/* void pin_function(PinName pin, int function);
   configure the mode, output mode, pull, speed, af function of pins
   the parameter function contains the configuration information,show as below
   bit 0:2     gpio mode input / output / af / analog
   bit 3       output push-pull / open drain
   bit 5:4     no pull, pull-up, pull-down
   bit 9:6     channel af function
   bit 11:10   gpio speed
   bit 16:12   channel of adc/timer/dac
   bit 17      PWM channel-ON
   bit 31:18   reserved
*/

/* Additional macros for UART, SPI, and CAN pin functions */
#define SET_UART_PIN_FUNCTION(MODE, OTYPE, PUPD, AF, CHANNEL) \
    SET_PIN_FUNCTION(MODE, OTYPE, PUPD, AF)

#define SET_SPI_PIN_FUNCTION(MODE, OTYPE, PUPD, AF, CHANNEL) \
    SET_PIN_FUNCTION(MODE, OTYPE, PUPD, AF)

#define SET_CAN_PIN_FUNCTION(MODE, OTYPE, PUPD, AF, CHANNEL) \
    SET_PIN_FUNCTION(MODE, OTYPE, PUPD, AF)

/* GPIO MODE */
const int GD_GPIO_MODE[] = {
    GPIO_MODE_INPUT,        /* 0 */
    GPIO_MODE_OUTPUT,       /* 1 */
    GPIO_MODE_AF,           /* 2 */
    GPIO_MODE_ANALOG,       /* 3 */
};

/* GPIO pull_up_down */
const int GD_GPIO_PULL_UP_DOWN[] = {
    GPIO_PUPD_NONE,        /* 0 */
    GPIO_PUPD_PULLUP,      /* 1 */
    GPIO_PUPD_PULLDOWN,    /* 2 */
};

/* GPIO otype */
const int GD_GPIO_OUTPUT_MODE[] = {
    GPIO_OTYPE_PP,        /* 0 */
    GPIO_OTYPE_OD,        /* 1 */
};

/* GPIO SPEED */
const int GD_GPIO_SPEED[] = {
    GPIO_OSPEED_50MHZ,            /* 0 */
    GPIO_OSPEED_200MHZ,           /* 1 */
    GPIO_OSPEED_25MHZ,            /* 2 */
    GPIO_OSPEED_2MHZ,             /* 3 */
};

/* GPIO AF */
const int GD_GPIO_AF[] = {
    GPIO_AF_0,              /* 0 */
    GPIO_AF_1,              /* 1 */
    GPIO_AF_2,              /* 2 */
    GPIO_AF_3,              /* 3 */
    GPIO_AF_4,              /* 4 */
    GPIO_AF_5,              /* 5 */
    GPIO_AF_6,              /* 6 */
    GPIO_AF_7,              /* 7 */
    GPIO_AF_8,              /* 8 */
    GPIO_AF_9,              /* 9 */
    GPIO_AF_10,             /* 10 */
    GPIO_AF_11,             /* 11 */
    GPIO_AF_12,             /* 12 */
    GPIO_AF_13,             /* 13 */
    GPIO_AF_14,             /* 14 */
    GPIO_AF_15,             /* 15 */
};

/* ADC PinMap */
const PinMap PinMap_ADC[] = {
    {PORTA_0, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(0)},    /* ADC0_IN0 */
    {PORTA_1, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(1)},    /* ADC0_IN1 */
    {PORTA_2, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(2)},    /* ADC0_IN2 */
    {PORTA_3, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(3)},    /* ADC0_IN3 */
    {PORTA_4, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(4)},    /* ADC0_IN4 */
    {PORTA_5, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(5)},    /* ADC0_IN5 */
    {PORTA_6, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(6)},    /* ADC0_IN6 */
    {PORTA_7, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(7)},    /* ADC0_IN7 */
    {PORTB_0, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(8)},    /* ADC0_IN8 */
    {PORTB_1, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(9)},    /* ADC0_IN9 */
    {PORTC_0, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(10)},   /* ADC0_IN10 */
    {PORTC_1, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(11)},   /* ADC0_IN11 */
    {PORTC_2, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(12)},   /* ADC0_IN12 */
    {PORTC_3, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(13)},   /* ADC0_IN13 */
    {PORTC_4, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(14)},   /* ADC0_IN14 */
    {PORTC_5, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(15)},   /* ADC0_IN15 */
    {ADC_TEMP, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(16)},  /* ADC0_IN16 - Temperature Sensor */
    {ADC_VREF, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(17)},  /* ADC0_IN17 - VREF */
    {ADC_VBAT, ADC_0, SET_PIN_FUNCTION_ADC_CHANNEL(18)},  /* ADC0_IN18 - VBAT */

    {PORTA_0_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(0)},   /* ADC1_IN0 */
    {PORTA_1_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(1)},   /* ADC1_IN1 */
    {PORTA_2_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(2)},   /* ADC1_IN2 */
    {PORTA_3_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(3)},   /* ADC1_IN3 */
    {PORTA_4_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(4)},   /* ADC1_IN4 */
    {PORTA_5_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(5)},   /* ADC1_IN5 */
    {PORTA_6_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(6)},   /* ADC1_IN6 */
    {PORTA_7_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(7)},   /* ADC1_IN7 */
    {PORTB_0_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(8)},   /* ADC1_IN8 */
    {PORTB_1_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(9)},   /* ADC1_IN9 */
    {PORTC_0_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(10)},  /* ADC1_IN10 */
    {PORTC_1_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(11)},  /* ADC1_IN11 */
    {PORTC_2_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(12)},  /* ADC1_IN12 */
    {PORTC_3_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(13)},  /* ADC1_IN13 */
    {PORTC_4_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(14)},  /* ADC1_IN14 */
    {PORTC_5_MUL0, ADC_1, SET_PIN_FUNCTION_ADC_CHANNEL(15)},  /* ADC1_IN15 */

    {PORTA_0_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(0)},   /* ADC2_IN0 */
    {PORTA_1_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(1)},   /* ADC2_IN1 */
    {PORTA_2_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(2)},   /* ADC2_IN2 */
    {PORTA_3_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(3)},   /* ADC2_IN3 */
    {PORTF_6,      ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(4)},   /* ADC2_IN4 */
    {PORTF_7,      ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(5)},   /* ADC2_IN5 */
    {PORTF_8,      ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(6)},   /* ADC2_IN6 */
    {PORTF_9,      ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(7)},   /* ADC2_IN7 */
    {PORTF_10,     ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(8)},   /* ADC2_IN8 */
    {PORTF_3,      ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(9)},   /* ADC2_IN9 */
    {PORTC_0_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(10)},  /* ADC2_IN10 */
    {PORTC_1_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(11)},  /* ADC2_IN11 */
    {PORTC_2_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(12)},  /* ADC2_IN12 */
    {PORTC_3_MUL1, ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(13)},  /* ADC2_IN13 */
    {PORTF_4,      ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(14)},  /* ADC2_IN14 */
    {PORTF_5,      ADC_2, SET_PIN_FUNCTION_ADC_CHANNEL(15)},  /* ADC2_IN15 */
    {NC,   NC,    0}
};

/* DAC PinMap */
const PinMap PinMap_DAC[] = {
    {PORTA_4,       DAC_0, SET_PIN_FUNCTION_DAC_CHANNEL(0)},    /* DAC_OUT0 */
    {PORTA_5,       DAC_0, SET_PIN_FUNCTION_DAC_CHANNEL(1)},    /* DAC_OUT1 */
    {NC, NC, 0}
};


/* I2C PinMap */
const PinMap PinMap_I2C_SDA[] = {
    {PORTB_3,       I2C_1, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_4,       I2C_2, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_7,       I2C_0, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_9,       I2C_0, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_11,      I2C_1, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTC_9,       I2C_2, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {NC,    NC,    0}
};

const PinMap PinMap_I2C_SCL[] = {
    {PORTA_8,       I2C_2, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_6,       I2C_0, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_8,       I2C_0, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {PORTB_10,      I2C_1, SET_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_OD, PIN_PUPD_NONE, PIN_AF_4)},
    {NC,    NC,    0}
};

/* PWM PinMap */
const PinMap PinMap_PWM[] = {
    {PORTA_0,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTA_1,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 0)},
    {PORTA_2,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 0)},
    {PORTA_2_MUL0,  PWM_8,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 0)},
    {PORTA_3,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 3, 0)},
    {PORTA_3_MUL0,  PWM_8,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 1, 0)},
    {PORTA_5,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTA_5_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 1)},
    {PORTA_6,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTA_6_MUL0,  PWM_12, SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0, 0)},
    {PORTA_7,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 1)},
    {PORTA_7_MUL0,  PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTA_7_MUL1,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 1)},
    {PORTA_7_MUL2,  PWM_13, SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0, 0)},
    {PORTA_8,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTA_9,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 0)},
    {PORTA_10,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 0)},
    {PORTA_11,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 3, 0)},
    {PORTA_15,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTB_0,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 1)},
    {PORTB_0_MUL0,  PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 2, 0)},
    {PORTB_0_MUL1,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 1, 1)},
    {PORTB_1,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 1)},
    {PORTB_1_MUL0,  PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 3, 0)},
    {PORTB_1_MUL1,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 2, 1)},
    {PORTB_3,       PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 0)},
    {PORTB_4,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTB_5,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTB_6,       PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTB_7,       PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTB_8,       PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 2, 0)},
    {PORTB_8_MUL0,  PWM_9,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 0)},
    {PORTB_9,       PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 3, 0)},
    {PORTB_9_MUL0,  PWM_10, SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 0)},
    {PORTB_10,      PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 0)},
    {PORTB_11,      PWM_1,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 3, 0)},
    {PORTB_13,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 1)},
    {PORTB_14,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 1)},
    {PORTB_14_MUL0, PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 1, 1)},
    {PORTB_14_MUL1, PWM_11, SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0, 0)},
    {PORTB_15,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 1)},
    {PORTB_15_MUL0, PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 2, 1)},
    {PORTB_15_MUL1, PWM_11, SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 1, 0)},
    {PORTC_6,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTC_6_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 0)},
    {PORTC_7,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTC_7_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 1, 0)},
    {PORTC_8,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 2, 0)},
    {PORTC_8_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 2, 0)},
    {PORTC_9,       PWM_2,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 3, 0)},
    {PORTC_9_MUL0,  PWM_7,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 3, 0)},
    {PORTD_12,      PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 0, 0)},
    {PORTD_13,      PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 1, 0)},
    {PORTD_14,      PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 2, 0)},
    {PORTD_15,      PWM_3,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_2, 3, 0)},
    {PORTE_5,       PWM_8,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 0)},
    {PORTE_6,       PWM_8,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 1, 0)},
    {PORTE_8,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 1)},
    {PORTE_9,       PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 0, 0)},
    {PORTE_10,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 1)},
    {PORTE_11,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 1, 0)},
    {PORTE_12,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 1)},
    {PORTE_13,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 2, 0)},
    {PORTE_14,      PWM_0,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_1, 3, 0)},
    {PORTF_6,       PWM_9,  SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 0)},
    {PORTF_7,       PWM_10, SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_3, 0, 0)},
    {PORTF_8,       PWM_12, SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0, 0)},
    {PORTF_9,       PWM_13, SET_PWM_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0, 0)},
    {NC,    NC,    0}
};

/* UART PinMap */
const PinMap PinMap_UART_TX[] = {
    {PORTA_0,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_2,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_9,       UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_15,      UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_6,       UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_10,      UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_6,       UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_10,      UART_3,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_5,       UART_4,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_8,       UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_0,       UART_7,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_8,       UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTF_7,       UART_8,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_UART_RX[] = {
    {PORTA_1,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_3,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_10,      UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_7,       UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_11,      UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_7,       UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_11,      UART_3,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_6,       UART_4,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_9,       UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_1,       UART_7,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_9,       UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTF_6,       UART_8,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_UART_RTS[] = {
    {PORTA_1,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_12,      UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_14,      UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_8,       UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_4,       UART_3,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_12,      UART_4,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_2,       UART_7,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_10,      UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTF_8,       UART_8,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_UART_CTS[] = {
    {PORTA_0,       UART_1,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTA_11,      UART_0,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTB_13,      UART_2,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTC_9,       UART_5,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_3,       UART_3,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTD_11,      UART_4,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_3,       UART_7,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTE_11,      UART_6,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {PORTF_9,       UART_8,  SET_UART_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_7, 0)},
    {NC,    NC,    0}
};

/* SPI PinMap */
const PinMap PinMap_SPI_MOSI[] = {
    {PORTA_7,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_7_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_5,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_5_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_15,      SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_15_MUL0, SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_3,       SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_12,      SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTD_7,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTE_6,       SPI_3,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTF_9,       SPI_4,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_SPI_MISO[] = {
    {PORTA_6,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_6_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_4,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_4_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_14,      SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_14_MUL0, SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_2,       SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_11,      SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTD_6,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTE_5,       SPI_3,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTF_8,       SPI_4,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_SPI_SCLK[] = {
    {PORTA_5,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_5_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_3,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_3_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_13,      SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_13_MUL0, SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_10,      SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTD_3,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTE_2,       SPI_3,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTF_7,       SPI_4,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_SPI_SSEL[] = {
    {PORTA_4,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_4_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_15,      SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTA_15_MUL0, SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_9,       SPI_0,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTB_9_MUL0,  SPI_1,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTC_9,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTD_0,       SPI_2,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTE_4,       SPI_3,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {PORTF_6,       SPI_4,  SET_SPI_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_5, 0)},
    {NC,    NC,    0}
};

/* CAN PinMap */
const PinMap PinMap_CAN_RD[] = {
    {PORTA_11,      CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTB_8,       CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTB_12,      CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTC_11,      CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTD_0,       CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTD_9,       CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTE_0,       CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTE_8,       CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTF_0,       CAN_3,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTF_11,      CAN_3,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {NC,    NC,    0}
};

const PinMap PinMap_CAN_TD[] = {
    {PORTA_12,      CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTB_9,       CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTB_13,      CAN_0,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTC_12,      CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTD_1,       CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTD_10,      CAN_1,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTE_1,       CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTE_9,       CAN_2,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTF_1,       CAN_3,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {PORTF_12,      CAN_3,  SET_CAN_PIN_FUNCTION(PIN_MODE_AF, PIN_OTYPE_PP, PIN_PUPD_PULLUP, PIN_AF_9, 0)},
    {NC,    NC,    0}
};
