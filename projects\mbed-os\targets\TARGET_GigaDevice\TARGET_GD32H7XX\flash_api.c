/* mbed Microcontroller Library
 * Copyright (c) 2024 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "flash_api.h"
#include "mbed_critical.h"
#include <string.h>

#if DEVICE_FLASH
#include "cmsis.h"

/* GD32H7XX Flash memory layout:
 * - Total size: 2MB (0x200000)
 * - Sector size: 4KB (0x1000) each
 * - Total sectors: 512 sectors
 * - Address range: 0x08000000 - 0x081FFFFF
 */
#define FLASH_SIZE                (0x00200000U)
#define FLASH_START_ADDR          (0x08000000U)
#define FLASH_END_ADDR            (0x081FFFFFU)
#define SECTOR_SIZE               (0x00001000U)  // 4KB per sector
#define WORD_SIZE                 (4U)

/** 初始化Flash外设和flash_t对象
 *
 * @param obj Flash对象
 * @return 成功返回0，错误返回-1
 */
int32_t flash_init(flash_t *obj)
{
    return 0;
}

/** 去初始化Flash外设和flash_t对象
 *
 * @param obj Flash对象
 * @return 成功返回0，错误返回-1
 */
int32_t flash_free(flash_t *obj)
{
    return 0;
}

/** 从定义的地址开始擦除一个扇区
 *
 * 地址应该在扇区边界上。此函数不对地址对齐进行任何检查
 * @param obj Flash对象
 * @param address 扇区起始地址
 * @return 成功返回0，错误返回-1
 */
int32_t flash_erase_sector(flash_t *obj, uint32_t address)
{
    int32_t flash_state = 0;
    
    /* Check if address is within valid range */
    if ((address < FLASH_START_ADDR) || (address > FLASH_END_ADDR)) {
        return -1;
    }
    
    /* Check if address is sector aligned */
    if ((address - FLASH_START_ADDR) % SECTOR_SIZE != 0) {
        return -1;
    }
    
    /* 解锁Flash */
    fmc_unlock();

    /* 清除所有标志 */
    fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_WPERR | FMC_FLAG_PGSERR |
                   FMC_FLAG_RPERR | FMC_FLAG_RSERR | FMC_FLAG_ECCCOR |
                   FMC_FLAG_ECCDET | FMC_FLAG_OBMERR);

    /* 擦除扇区 */
    if (FMC_READY != fmc_sector_erase(address)) {
        flash_state = -1;
    }

    /* 锁定Flash */
    fmc_lock();
    
    return flash_state;
}

/** 从定义的地址开始编程页面
 *
 * 页面不应跨越多个扇区。
 * 此函数不对地址对齐或大小是否与页面大小对齐进行任何检查。
 * @param obj Flash对象
 * @param address 扇区起始地址
 * @param data 要编程的数据缓冲区
 * @param size 要编程的字节数
 * @return 成功返回0，错误返回-1
 */
int32_t flash_program_page(flash_t *obj, uint32_t address, const uint8_t *data, uint32_t size)
{
    uint32_t *p_data;
    uint32_t read_data = 0;
    uint32_t write_data = 0;
    p_data = (uint32_t *)data;
    uint32_t num = 0;
    int32_t flash_state = 0;
    
    /* Check if address is within valid range */
    if ((address < FLASH_START_ADDR) || (address + size > FLASH_END_ADDR + 1)) {
        return -1;
    }
    
    /* Unlock flash */
    fmc_unlock();
    
    /* Clear all flags */
    fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_WPERR | FMC_FLAG_PGSERR | 
                   FMC_FLAG_RPERR | FMC_FLAG_RSERR | FMC_FLAG_ECCCOR | 
                   FMC_FLAG_ECCDET | FMC_FLAG_OBMERR);
    
    /* Calculate number of words to program */
    if (size % 4) {
        num = size / 4 + 1;
    } else {
        num = size / 4;
    }
    
    /* Program words */
    for (uint32_t i = 0; i < num; i++) {
        if (FMC_READY != fmc_word_program(address, *(p_data + i))) {
            flash_state = -1;
            break;
        }
        
        /* Verify written data */
        write_data = *(p_data + i);
        memcpy(&read_data, (void *)address, 4);
        if (read_data != write_data) {
            flash_state = -1;
            break;
        }
        address += 4;
    }
    
    /* Lock flash */
    fmc_lock();
    
    return flash_state;
}

/** Get sector size
 *
 * @param obj The flash object
 * @param address The sector starting address
 * @return The size of a sector
 */
uint32_t flash_get_sector_size(const flash_t *obj, uint32_t address)
{
    /* All sectors are 4KB in GD32H7XX */
    return SECTOR_SIZE;
}

/** Get page size
 *
 * The page size defines the writable page size
 * @param obj The flash object
 * @return The size of a page
 */
uint32_t flash_get_page_size(const flash_t *obj)
{
    return WORD_SIZE;
}

/** Get start address for the flash region
 *
 * @param obj The flash object
 * @return The start address for the flash region
 */
uint32_t flash_get_start_address(const flash_t *obj)
{
    return FLASH_START_ADDR;
}

/** Get the flash region size
 *
 * @param obj The flash object
 * @return The flash region size
 */
uint32_t flash_get_size(const flash_t *obj)
{
    return FLASH_SIZE;
}

/** Get the flash erase value
 *
 * @param obj The flash object
 * @return The flash erase value
 */
uint8_t flash_get_erase_value(const flash_t *obj)
{
    return 0xFF;
}

#endif /* DEVICE_FLASH */
