/* mbed Microcontroller Library
 * Copyright (c) 2024 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "gd32h7xx.h"
#include "us_ticker_api.h"
#include "PeripheralNames.h"
#include "hal_tick.h"

static volatile uint32_t g_systickCounter = 0;
static volatile bool g_pit_initialized = ERROR;
static volatile uint32_t g_pit_count_max = 1000;
static volatile uint32_t g_pit_us_last = 0;
static volatile uint32_t g_micros_last = 0;
static volatile uint32_t g_millis_last = 0;

#if TICKER_TIMER_WIDTH_BIT == 16
static uint32_t time_before;
static uint32_t total_elapsed_time;
#endif

/* this variable is set to 1 at the end of mbed_sdk_init function.
the ticker_read_us() function must not be called until the mbed_sdk_init is terminated */
extern int mbed_sdk_inited;
static uint32_t ticker_timer_cnt;
static uint32_t ticker_timer_ch0cv;
static uint32_t ticker_timer_dmainten;

void TIMER6_IRQHandler() {
  if(timer_interrupt_flag_get(TIMER6,TIMER_INT_UP) == SET){
    g_systickCounter++;      
    timer_interrupt_flag_clear(TIMER6, TIMER_INT_UP); 
  }
}

void ticker_timer_init(void);
#if TICKER_TIMER_WIDTH_BIT == 16
void ticker_16bits_timer_init(void);
#else
void ticker_32bits_timer_init(void);
#endif
void ticker_timer_irq_handler(void);
/* get TIMER clock */
static uint32_t timer_get_clock(uint32_t timer_periph);
uint32_t ticker_tick_get(void);
void ticker_timer_data_save(void);
void ticker_timer_data_restore(void);

void ticker_timer_init(void)
{
#if TICKER_TIMER_WIDTH_BIT == 16
    ticker_16bits_timer_init();
#else
    ticker_32bits_timer_init();
#endif
}

/** get tick
 *
 * @return the tick
 */
uint32_t ticker_tick_get(void)
{
#if TICKER_TIMER_WIDTH_BIT == 16
    uint32_t new_time;
    if (mbed_sdk_inited) {
        /* Apply the latest time recorded just before the sdk is inited */
        new_time = ticker_read_us(get_us_ticker_data()) + time_before;
        time_before = 0;
        return (new_time / 1000);
    } else {
        /* Prevent small values from subtracting large ones
        example:
                0x0010-0xFFEE=FFFF0022 , (0xFFFF-0xFFEE+0x10+1=0x22,1 mean CNT=0 tick)
                FFFF0022 & 0xFFFF = 0022
        */
        new_time = us_ticker_read();
        total_elapsed_time += (new_time - time_before) & 0xFFFF;
        time_before = new_time;
        return (total_elapsed_time / 1000);
    }
#else // 32-bit timer
    if (mbed_sdk_inited) {
        return (ticker_read_us(get_us_ticker_data()) / 1000);
    } else {
        return (us_ticker_read() / 1000);
    }
#endif
}

/** Get frequency and counter bits of this ticker.
 */
const ticker_info_t *us_ticker_get_info()
{
    static const ticker_info_t info = {
        1000000,
        TICKER_TIMER_WIDTH_BIT
    };
    return &info;
}

/* config for 16bits TIMER */
#if TICKER_TIMER_WIDTH_BIT == 16
/** config the interrupt handler
 */
void ticker_timer_irq_handler(void)
{
    if (SET == timer_interrupt_flag_get(TICKER_TIMER, TIMER_INT_FLAG_CH0)) {
        timer_interrupt_flag_clear(TICKER_TIMER, TIMER_INT_FLAG_CH0);
        us_ticker_irq_handler();
    }
}

/** initialize the TIMER
 */
void ticker_16bits_timer_init(void)
{
    timer_parameter_struct timer_initpara;
    uint32_t timer_clk = timer_get_clock(TICKER_TIMER);

    /* enable ticker timer clock */
    TICKER_TIMER_RCU_CLOCK_ENABLE;

    /* reset ticker timer peripheral */
    TICKER_TIMER_RESET_ENABLE;
    TICKER_TIMER_RESET_DISABLE;

    /* TICKER_TIMER configuration */
    timer_initpara.prescaler         = (uint32_t)(timer_clk / 1000000) - 1;;
    timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
    timer_initpara.counterdirection  = TIMER_COUNTER_UP;
    timer_initpara.period            = 0xFFFF;
    timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
    timer_initpara.repetitioncounter = 0;
    timer_init(TICKER_TIMER, &timer_initpara);

    /* auto-reload preload disable */
    timer_auto_reload_shadow_disable(TICKER_TIMER);

    /* configure TIMER channel enable state */
    timer_channel_output_state_config(TICKER_TIMER, TIMER_CH_0, TIMER_CCX_ENABLE);

    /* configure TIMER primary output function */
    timer_primary_output_config(TICKER_TIMER, ENABLE);

    timer_enable(TICKER_TIMER);

    /* Output compare channel 0 interrupt for mbed timeout */
    NVIC_SetVector(TICKER_TIMER_IRQ, (uint32_t)ticker_timer_irq_handler);
    NVIC_EnableIRQ(TICKER_TIMER_IRQ);

    /* if define the FREEZE_TIMER_ON_DEBUG macro in mbed_app.json or other file,
       hold the TICKER_TIMER counter for debug when core halted
    */
#if !defined(NDEBUG) && defined(FREEZE_TIMER_ON_DEBUG) && defined(TICKER_TIMER_DEBUG_STOP)
    TICKER_TIMER_DEBUG_STOP;
#endif

    timer_interrupt_disable(TICKER_TIMER, TIMER_INT_CH0);

    /* used by ticker_tick_get() */
    time_before = 0;
    total_elapsed_time = 0;
}
/* config for 32bits TIMER */
#else
/** config the interrupt handler
 */
void ticker_timer_irq_handler(void)
{
    if (SET == timer_interrupt_flag_get(TICKER_TIMER, TIMER_INT_FLAG_CH0)) {
        timer_interrupt_flag_clear(TICKER_TIMER, TIMER_INT_FLAG_CH0);
        us_ticker_irq_handler();
    }
}

/** initialize the TIMER
 */
void ticker_32bits_timer_init(void)
{
    timer_parameter_struct timer_initpara;
    uint32_t timer_clk = timer_get_clock(TICKER_TIMER);

    /* enable ticker timer clock */
    TICKER_TIMER_RCU_CLOCK_ENABLE;

    /* reset ticker timer peripheral */
    TICKER_TIMER_RESET_ENABLE;
    TICKER_TIMER_RESET_DISABLE;

    /* TICKER_TIMER configuration */
    timer_initpara.prescaler         = (uint32_t)(timer_clk / 1000000) - 1;;
    timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
    timer_initpara.counterdirection  = TIMER_COUNTER_UP;
    timer_initpara.period            = 0xFFFFFFFF;
    timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
    timer_initpara.repetitioncounter = 0;
    timer_init(TICKER_TIMER, &timer_initpara);

    /* auto-reload preload disable */
    timer_auto_reload_shadow_disable(TICKER_TIMER);

    /* configure TIMER channel enable state */
    timer_channel_output_state_config(TICKER_TIMER, TIMER_CH_0, TIMER_CCX_ENABLE);

    /* configure TIMER primary output function */
    timer_primary_output_config(TICKER_TIMER, ENABLE);

    timer_enable(TICKER_TIMER);

    /* Output compare channel 0 interrupt for mbed timeout */
    NVIC_SetVector(TICKER_TIMER_IRQ, (uint32_t)ticker_timer_irq_handler);
    NVIC_EnableIRQ(TICKER_TIMER_IRQ);

    /* if define the FREEZE_TIMER_ON_DEBUG macro in mbed_app.json or other file,
       hold the TICKER_TIMER counter for debug when core halted
    */
#if !defined(NDEBUG) && defined(FREEZE_TIMER_ON_DEBUG) && defined(TICKER_TIMER_DEBUG_STOP)
    TICKER_TIMER_DEBUG_STOP;
#endif

    timer_interrupt_disable(TICKER_TIMER, TIMER_INT_CH0);
}

#endif /* 16-bit/32-bit timer init */

/** Initialize the ticker
 */
void us_ticker_init(void)
{
  timer_parameter_struct timer_initpara;

  rcu_periph_clock_enable(RCU_TIMER6);
  rcu_timer_clock_prescaler_config(RCU_TIMER_PSC_MUL4);

  timer_deinit(TIMER6);

  /* TIMER6 configuration */
  timer_initpara.prescaler         = SystemCoreClock / 1000000 -1;
  timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
  timer_initpara.counterdirection  = TIMER_COUNTER_UP;
  timer_initpara.period            = 1000;
  timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
  timer_initpara.repetitioncounter = 0;
  timer_init(TIMER6,&timer_initpara);

  /* auto-reload preload enable */
  timer_auto_reload_shadow_enable(TIMER6);
  /* TIMER6 enable */
  timer_enable(TIMER6);

  timer_flag_clear(TIMER6,TIMER_FLAG_UP);
  timer_interrupt_enable(TIMER6,TIMER_INT_UP);

  nvic_irq_enable(TIMER6_IRQn,0U,0U);

  g_pit_initialized = SUCCESS;
}

uint32_t ms_ticker_read() {
    return g_systickCounter;
}

/** Read the current counter
 */
uint32_t us_ticker_read()
{
  if (!g_pit_initialized) {
    return 0;
  }
  __disable_irq();

  uint32_t ms_count;
  uint32_t ms_count_init;
  uint32_t pit_cval;
  uint32_t pit_count;
  uint32_t pit_us;

  ms_count = g_systickCounter;
  ms_count_init = ms_count;

  pit_cval = timer_counter_read(TIMER6);

  pit_count = pit_cval;
  pit_us = (pit_count * 1000) / g_pit_count_max;

  // rollover
  if (pit_us < g_pit_us_last) {
    if (g_millis_last >= ms_count) {
      ms_count++;
    }
  }

  uint32_t us_from_ms_count = ms_count * 1000;
  uint32_t us_total = us_from_ms_count + pit_us;

  if (us_total < g_micros_last) {
    // uint8_t state = 3;
  }

  g_pit_us_last = pit_us;
  g_micros_last = us_total;
  g_millis_last = ms_count;

  __enable_irq();
  return us_total;
}

/** Set interrupt for specified timestamp
 */
void us_ticker_set_interrupt(timestamp_t timestamp)
{
    /* configure TIMER channel output pulse value.Only set this value when you interrupt disabled */
    timer_channel_output_pulse_value_config(TICKER_TIMER, TIMER_CH_0, (uint32_t)timestamp);
    /* clear TIMER interrupt flag,enable the TIMER interrupt */
    timer_interrupt_flag_clear(TICKER_TIMER, TIMER_INT_FLAG_CH0);
    timer_interrupt_enable(TICKER_TIMER, TIMER_INT_CH0);
}

/** Set pending interrupt that should be fired right away.
 */
void us_ticker_fire_interrupt(void)
{
    /* clear TIMER interrupt flag */
    timer_interrupt_flag_clear(TICKER_TIMER, TIMER_INT_FLAG_CH0);
    /* channel 0 capture or compare event generation immediately,so CH0IF set for interrupt */
    timer_event_software_generate(TICKER_TIMER, TIMER_EVENT_SRC_CH0G);
    /* enable the TIMER interrupt */
    timer_interrupt_enable(TICKER_TIMER, TIMER_INT_CH0);
}

/** Disable us ticker interrupt
 */
void us_ticker_disable_interrupt(void)
{
    /* disable the TIMER interrupt */
    timer_interrupt_disable(TICKER_TIMER, TIMER_INT_CH0);
}

/** Clear us ticker interrupt
 */
void us_ticker_clear_interrupt(void)
{
    /* clear TIMER interrupt flag */
    timer_interrupt_flag_clear(TICKER_TIMER, TIMER_INT_FLAG_CH0);
}

/** save ticker TIMER data when MCU go to deepsleep
*/
void ticker_timer_data_save(void)
{
    ticker_timer_cnt = TIMER_CNT(TICKER_TIMER);
    ticker_timer_ch0cv = TIMER_CH0CV(TICKER_TIMER);
    ticker_timer_dmainten = TIMER_DMAINTEN(TICKER_TIMER);
}

/** restore ticker TIMER data when MCU go out deepsleep
*/
void ticker_timer_data_restore(void)
{
    TIMER_CNT(TICKER_TIMER) = ticker_timer_cnt;
    TIMER_CH0CV(TICKER_TIMER) = ticker_timer_ch0cv;
    TIMER_DMAINTEN(TICKER_TIMER) = ticker_timer_dmainten;
}

/** Deinitialize the us ticker
 */
void us_ticker_free(void)
{
    /* configure TIMER channel enable state */
    timer_channel_output_state_config(TICKER_TIMER, TIMER_CH_0, TIMER_CCX_DISABLE);
    /* configure TIMER primary output function */
    timer_primary_output_config(TICKER_TIMER, DISABLE);
    /* disable a TIMER */
    timer_disable(TICKER_TIMER);

    us_ticker_disable_interrupt();
}

/** get TIMER clock
*/
static uint32_t timer_get_clock(uint32_t timer_periph)
{
    uint32_t timerclk;

    if ((TIMER0 == timer_periph) || (TIMER7 == timer_periph) ||
            (TIMER8 == timer_periph) || (TIMER9 == timer_periph) || (TIMER10 == timer_periph)) {
        /* get the current APB2 TIMER clock source */
        if (RCU_APB2_CKAHB_DIV1 == (RCU_CFG0 & RCU_CFG0_APB2PSC)) {
            timerclk = rcu_clock_freq_get(CK_APB2);
        } else {
            timerclk = rcu_clock_freq_get(CK_APB2) * 2;
        }
    } else {
        /* get the current APB1 TIMER clock source */
        if (RCU_APB1_CKAHB_DIV1 == (RCU_CFG0 & RCU_CFG0_APB1PSC)) {
            timerclk = rcu_clock_freq_get(CK_APB1);
        } else {
            timerclk = rcu_clock_freq_get(CK_APB1) * 2;
        }
    }

    return timerclk;
}
