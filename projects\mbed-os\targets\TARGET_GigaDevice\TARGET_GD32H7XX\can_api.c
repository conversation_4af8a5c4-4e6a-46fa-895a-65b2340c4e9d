/* mbed Microcontroller Library
 * Copyright (c) 2024 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "can_api.h"

#if DEVICE_CAN

#include "cmsis.h"
#include "pinmap.h"
#include "PeripheralPins.h"
#include "mbed_error.h"

static uint32_t can_irq_ids[2] = {0};
static can_irq_handler irq_handler;

/** 初始化CAN外设
 *
 * @param obj     CAN对象
 * @param rd      CAN接收引脚名称
 * @param td      CAN发送引脚名称
 */
void can_init(can_t *obj, PinName rd, PinName td)
{
    CANName can_rd = (CANName)pinmap_peripheral(rd, PinMap_CAN_RD);
    CANName can_td = (CANName)pinmap_peripheral(td, PinMap_CAN_TD);

    obj->can = (CANName)pinmap_merge(can_rd, can_td);
    MBED_ASSERT((int)obj->can != NC);

    /* 配置GPIO引脚 */
    pinmap_pinout(rd, PinMap_CAN_RD);
    pinmap_pinout(td, PinMap_CAN_TD);

    /* 使能CAN时钟 */
    if (obj->can == CAN_0) {
        obj->index = 0;
        rcu_can_clock_config(IDX_CAN1, RCU_CANSRC_APB2);
        rcu_periph_clock_enable(RCU_CAN1);
    } else if (obj->can == CAN_1) {
        obj->index = 1;
        rcu_can_clock_config(IDX_CAN2, RCU_CANSRC_APB2);
        rcu_periph_clock_enable(RCU_CAN2);
    }

    /* 初始化CAN参数 */
    can_parameter_struct can_parameter;
    can_struct_para_init(CAN_INIT_STRUCT, &can_parameter);

    /* 配置CAN参数为125kbps */
    can_parameter.internal_counter_source = CAN_TIMER_SOURCE_BIT_CLOCK;
    can_parameter.self_reception = DISABLE;
    can_parameter.mb_tx_order = CAN_TX_HIGH_PRIORITY_MB_FIRST;
    can_parameter.mb_tx_abort_enable = ENABLE;
    can_parameter.local_priority_enable = DISABLE;
    can_parameter.mb_rx_ide_rtr_type = CAN_IDE_RTR_FILTERED;
    can_parameter.mb_remote_frame = CAN_STORE_REMOTE_REQUEST_FRAME;
    can_parameter.rx_private_filter_queue_enable = DISABLE;
    can_parameter.edge_filter_enable = DISABLE;
    can_parameter.protocol_exception_enable = DISABLE;
    can_parameter.rx_filter_order = CAN_RX_FILTER_ORDER_MAILBOX_FIRST;
    can_parameter.memory_size = CAN_MEMSIZE_32_UNIT;
    can_parameter.mb_public_filter = 0U;

    /* 波特率125kbps，采样点在80% */
    can_parameter.resync_jump_width = 1U;
    can_parameter.prop_time_segment = 2U;
    can_parameter.time_segment_1 = 5U;
    can_parameter.time_segment_2 = 2U;
    can_parameter.prescaler = 240U;  /* 600MHz APB2时钟下的125kbps */

    /* 初始化CAN */
    can_deinit(obj->can);
    can_init(obj->can, &can_parameter);

    /* 进入正常模式 */
    can_operation_mode_enter(obj->can, CAN_NORMAL_MODE);

    /* 配置接收邮箱0 */
    can_mailbox_descriptor_struct rx_mailbox;
    can_struct_para_init(CAN_MDSC_STRUCT, &rx_mailbox);
    rx_mailbox.rtr = 0U;
    rx_mailbox.ide = 0U;
    rx_mailbox.code = CAN_MB_RX_STATUS_EMPTY;
    rx_mailbox.id = 0x0U;  /* 接受所有ID */
    can_mailbox_config(obj->can, 0U, &rx_mailbox);
}

/** 去初始化CAN外设
 *
 * @param obj CAN对象
 */
void can_free(can_t *obj)
{
    can_deinit(obj->can);
    
    if (obj->can == CAN_0) {
        rcu_periph_clock_disable(RCU_CAN1);
    } else if (obj->can == CAN_1) {
        rcu_periph_clock_disable(RCU_CAN2);
    }
}

/** 发送CAN消息
 *
 * @param obj CAN对象
 * @param msg 要发送的CAN消息
 * @return 成功返回1，否则返回0
 */
int can_write(can_t *obj, CAN_Message msg, int cc)
{
    can_mailbox_descriptor_struct tx_mailbox;
    can_struct_para_init(CAN_MDSC_STRUCT, &tx_mailbox);

    /* 配置发送消息 */
    tx_mailbox.rtr = (msg.type == CANRemote) ? 1U : 0U;
    tx_mailbox.ide = (msg.format == CANExtended) ? 1U : 0U;
    tx_mailbox.code = CAN_MB_TX_STATUS_DATA;
    tx_mailbox.brs = 0U;
    tx_mailbox.fdf = 0U;
    tx_mailbox.prio = 0U;
    tx_mailbox.data_bytes = msg.len;
    tx_mailbox.id = msg.id;
    tx_mailbox.data = (uint32_t *)msg.data;

    /* 使用邮箱1进行发送 */
    can_mailbox_config(obj->can, 1U, &tx_mailbox);

    /* 等待发送完成 */
    uint32_t timeout = 1000000;
    while ((RESET == can_flag_get(obj->can, CAN_FLAG_MB1)) && (timeout > 0)) {
        timeout--;
    }

    if (timeout == 0) {
        return 0;  /* 发送失败 */
    }

    can_flag_clear(obj->can, CAN_FLAG_MB1);
    return 1;  /* 发送成功 */
}

/** 读取CAN消息
 *
 * @param obj CAN对象
 * @param msg 指向要填充的CAN消息结构的指针
 * @param handle 消息句柄（未使用）
 * @return 如果读取到消息返回1，否则返回0
 */
int can_read(can_t *obj, CAN_Message *msg, int handle)
{
    /* Check if mailbox 0 has received data */
    if (RESET != can_flag_get(obj->can, CAN_FLAG_MB0)) {
        can_mailbox_descriptor_struct rx_mailbox;
        can_struct_para_init(CAN_MDSC_STRUCT, &rx_mailbox);
        rx_mailbox.data = (uint32_t *)msg->data;

        /* Read received message */
        can_mailbox_receive_data_read(obj->can, 0U, &rx_mailbox);

        /* Fill message structure */
        msg->id = rx_mailbox.id;
        msg->len = rx_mailbox.data_bytes;
        msg->format = (rx_mailbox.ide == 1U) ? CANExtended : CANStandard;
        msg->type = (rx_mailbox.rtr == 1U) ? CANRemote : CANData;

        /* Clear flag */
        can_flag_clear(obj->can, CAN_FLAG_MB0);

        /* Reconfigure mailbox for next reception */
        rx_mailbox.code = CAN_MB_RX_STATUS_EMPTY;
        can_mailbox_config(obj->can, 0U, &rx_mailbox);

        return 1;
    }

    return 0;
}

/** Set the frequency of the CAN interface
 *
 * @param obj The CAN object
 * @param hz  The bus frequency in Hz
 * @return 1 if successful, 0 otherwise
 */
int can_frequency(can_t *obj, int hz)
{
    can_parameter_struct can_parameter;
    can_struct_para_init(CAN_INIT_STRUCT, &can_parameter);

    /* Configure basic parameters */
    can_parameter.internal_counter_source = CAN_TIMER_SOURCE_BIT_CLOCK;
    can_parameter.self_reception = DISABLE;
    can_parameter.mb_tx_order = CAN_TX_HIGH_PRIORITY_MB_FIRST;
    can_parameter.mb_tx_abort_enable = ENABLE;
    can_parameter.local_priority_enable = DISABLE;
    can_parameter.mb_rx_ide_rtr_type = CAN_IDE_RTR_FILTERED;
    can_parameter.mb_remote_frame = CAN_STORE_REMOTE_REQUEST_FRAME;
    can_parameter.rx_private_filter_queue_enable = DISABLE;
    can_parameter.edge_filter_enable = DISABLE;
    can_parameter.protocol_exception_enable = DISABLE;
    can_parameter.rx_filter_order = CAN_RX_FILTER_ORDER_MAILBOX_FIRST;
    can_parameter.memory_size = CAN_MEMSIZE_32_UNIT;
    can_parameter.mb_public_filter = 0U;

    /* Calculate timing parameters based on frequency */
    /* Assuming APB2 clock is 600MHz */
    uint32_t apb2_freq = 600000000;
    uint32_t prescaler;
    
    /* Standard timing: 80% sample point */
    can_parameter.resync_jump_width = 1U;
    can_parameter.prop_time_segment = 2U;
    can_parameter.time_segment_1 = 5U;
    can_parameter.time_segment_2 = 2U;
    
    /* Total time quanta = 1 + prop + ts1 + ts2 = 1 + 2 + 5 + 2 = 10 */
    prescaler = apb2_freq / (hz * 10);
    
    if (prescaler > 1024 || prescaler < 1) {
        return 0;  /* Invalid frequency */
    }
    
    can_parameter.prescaler = prescaler;

    /* Reinitialize CAN with new parameters */
    can_deinit(obj->can);
    can_init(obj->can, &can_parameter);
    can_operation_mode_enter(obj->can, CAN_NORMAL_MODE);

    return 1;
}

/** Set up interrupt handler
 *
 * @param obj     The CAN object
 * @param handler The CAN IRQ handler
 * @param id      The object ID
 */
void can_irq_set(can_t *obj, can_irq_handler handler, uint32_t id)
{
    irq_handler = handler;
    can_irq_ids[obj->index] = id;
}

/** Enable CAN interrupts
 *
 * @param obj    The CAN object
 * @param irq    The CAN IRQ type
 * @param enable 1 to enable, 0 to disable
 */
void can_irq_enable(can_t *obj, CanIrqType irq, uint32_t enable)
{
    if (enable) {
        switch (irq) {
            case IRQ_RX:
                can_interrupt_enable(obj->can, CAN_INT_MB0);
                if (obj->can == CAN_0) {
                    NVIC_EnableIRQ(CAN1_Message_IRQn);
                } else if (obj->can == CAN_1) {
                    NVIC_EnableIRQ(CAN2_Message_IRQn);
                }
                break;
            case IRQ_TX:
                can_interrupt_enable(obj->can, CAN_INT_MB1);
                if (obj->can == CAN_0) {
                    NVIC_EnableIRQ(CAN1_Message_IRQn);
                } else if (obj->can == CAN_1) {
                    NVIC_EnableIRQ(CAN2_Message_IRQn);
                }
                break;
            case IRQ_ERROR:
            case IRQ_PASSIVE:
            case IRQ_BUS:
                /* Error interrupts can be added here if needed */
                break;
        }
    } else {
        switch (irq) {
            case IRQ_RX:
                can_interrupt_disable(obj->can, CAN_INT_MB0);
                break;
            case IRQ_TX:
                can_interrupt_disable(obj->can, CAN_INT_MB1);
                break;
            case IRQ_ERROR:
            case IRQ_PASSIVE:
            case IRQ_BUS:
                /* Error interrupts can be added here if needed */
                break;
        }
    }
}

/** Check if CAN peripheral is writable
 *
 * @param obj The CAN object
 * @return 1 if writable, 0 otherwise
 */
unsigned char can_rderror(can_t *obj)
{
    /* Read error counter - implementation depends on specific register access */
    return 0;  /* Placeholder */
}

/** Check if CAN peripheral is readable
 *
 * @param obj The CAN object
 * @return 1 if readable, 0 otherwise
 */
unsigned char can_tderror(can_t *obj)
{
    /* Transmit error counter - implementation depends on specific register access */
    return 0;  /* Placeholder */
}

/** Set CAN mode
 *
 * @param obj  The CAN object
 * @param mode The CAN mode
 */
void can_monitor(can_t *obj, int silent)
{
    if (silent) {
        /* Enter listen-only mode */
        can_operation_mode_enter(obj->can, CAN_LISTEN_ONLY_MODE);
    } else {
        /* Enter normal mode */
        can_operation_mode_enter(obj->can, CAN_NORMAL_MODE);
    }
}

/** CAN1 Message interrupt handler
 */
void CAN1_Message_IRQHandler(void)
{
    if (RESET != can_interrupt_flag_get(CAN1, CAN_INT_FLAG_MB0)) {
        can_interrupt_flag_clear(CAN1, CAN_INT_FLAG_MB0);
        if (irq_handler) {
            irq_handler(can_irq_ids[0], IRQ_RX);
        }
    }

    if (RESET != can_interrupt_flag_get(CAN1, CAN_INT_FLAG_MB1)) {
        can_interrupt_flag_clear(CAN1, CAN_INT_FLAG_MB1);
        if (irq_handler) {
            irq_handler(can_irq_ids[0], IRQ_TX);
        }
    }
}

/** CAN2 Message interrupt handler
 */
void CAN2_Message_IRQHandler(void)
{
    if (RESET != can_interrupt_flag_get(CAN2, CAN_INT_FLAG_MB0)) {
        can_interrupt_flag_clear(CAN2, CAN_INT_FLAG_MB0);
        if (irq_handler) {
            irq_handler(can_irq_ids[1], IRQ_RX);
        }
    }

    if (RESET != can_interrupt_flag_get(CAN2, CAN_INT_FLAG_MB1)) {
        can_interrupt_flag_clear(CAN2, CAN_INT_FLAG_MB1);
        if (irq_handler) {
            irq_handler(can_irq_ids[1], IRQ_TX);
        }
    }
}

#endif /* DEVICE_CAN */
